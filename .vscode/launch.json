{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Run",
            "type": "debugpy",
            "request": "launch",
            "program": "/fs-computility/llmeval/zhudongsheng/program/tau-bench/run.py",
            "python": "/fs-computility/llmeval/zhudongsheng/miniconda3/envs/tau_bench/bin/python",
            "args": [
                "--agent-strategy", "tool-calling",
                "--env", "retail",
                "--model", "claude-3-7-sonnet-20250219",
                "--model-provider", "openai",
                "--base-url", "https://api.anthropic.com/v1/",
                "--user-model", "gpt-4o",
                "--user-model-provider","openai",
                "--user-strategy", "llm",
                "--max-concurrency", "10",
            ],
            "console": "integratedTerminal",
            "env": {
                // // "CUDA_VISIBLE_DEVICES": "0,1",
                "OPENAI_API_KEY": "************************************************************************************************************", 
                "DEPLOY_BASE_URL": "http://172.30.52.140:23333/v1",
                "OPENAI_PROXY_URL": "http://closeai-proxy.pjlab.org.cn:23128", 
                "DEPLOY_API_KEY": "YOUR_API_KEY",
                // "HTTP_PROXY": "http://closeai-proxy.pjlab.org.cn:23128", 
                // "https_proxy": "http://closeai-proxy.pjlab.org.cn:23128",
                // "http_proxy": "http://closeai-proxy.pjlab.org.cn:23128",
            }
        }
    ]
}